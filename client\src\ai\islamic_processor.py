"""
Islamic AI Processor for the Hifzy Voice Assistant

This module integrates the Islamic AI backend functionality into the existing
voice assistant architecture by importing and adapting the ai_backend services.
"""

import logging
import sys
import os
# from typing import Optional

# Add the ai_backend directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "ai_backend"))

from src.ai.processor import AIProcessor
from src.config.settings import Config, IslamicAIConfig


class IslamicAIProcessor(AIProcessor):
    """
    Islamic AI processor that integrates ai_backend functionality
    into the existing AIProcessor interface
    """

    def __init__(self, config: Config):
        """
        Initialize the Islamic AI processor

        Args:
            config: Main configuration object containing Islamic AI settings
        """
        self.config = config
        self.islamic_config = config.islamic_ai
        self.logger = logging.getLogger(__name__)

        # Initialize services
        self.deepl_service = None
        self.langgraph_service = None
        self.openai_service = None
        self._services_initialized = False

        # Check if required configuration is available
        if not self._check_configuration():
            self.logger.warning("Islamic AI configuration incomplete. Some features may not work.")

        self.logger.info("Islamic AI processor initialized")

    def _check_configuration(self) -> bool:
        """
        Check if the required configuration is available

        Returns:
            True if configuration is sufficient, False otherwise
        """
        required_keys = ["openai_api_key", "deepl_api_key", "tavily_api_key"]

        missing_keys = []
        for key in required_keys:
            if not getattr(self.islamic_config, key):
                missing_keys.append(key)

        if missing_keys:
            self.logger.warning(f"Missing required configuration: {missing_keys}")
            return False

        return True

    def _initialize_services(self):
        """
        Lazy initialization of ai_backend services
        """
        if self._services_initialized:
            return

        try:
            # Create a mock settings object that ai_backend expects
            self._create_ai_backend_settings()

            # Import and initialize ai_backend services
            from services.deepL_service import Deepl_Service
            from services.langgraph_service import LanggraphService
            from services.open_ai_service import openai_service

            # Initialize services
            self.deepl_service = Deepl_Service()

            # Create qdrant configs for LanggraphService
            qdrant_configs = self._create_qdrant_configs()
            self.langgraph_service = LanggraphService(qdrant_configs)

            # Use the global openai_service instance
            self.openai_service = openai_service

            self._services_initialized = True
            self.logger.info("AI backend services initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI backend services: {e}")
            self._services_initialized = False
            raise

    def _create_ai_backend_settings(self):
        """
        Create a settings object that ai_backend expects
        """
        # Import the settings module from ai_backend
        import core.config as ai_backend_config

        # Create a mock settings object with our configuration
        class MockSettings:
            def __init__(self, islamic_config: IslamicAIConfig):
                self.VERSION = islamic_config.version
                self.LOGGING_DIR = islamic_config.logging_dir
                self.LLM_MODEL = islamic_config.llm_model
                self.EMBEDDING_MODEL = islamic_config.embedding_model

                self.QURAN_COLLECTION_NAME = islamic_config.quran_collection_name
                self.HADITH_COLLECTION_NAME = islamic_config.hadith_collection_name
                self.TAFSEER_COLLECTION_NAME = islamic_config.tafseer_collection_name
                self.ISLAMIC_INFO_COLLECTION_NAME = islamic_config.islamic_info_collection_name

                self.DEEPL_API_KEY = islamic_config.deepl_api_key
                self.GROQ_API_KEY = islamic_config.groq_api_key
                self.TAVILY_API_KEY = islamic_config.tavily_api_key
                self.OPENAI_API_KEY = islamic_config.openai_api_key
                self.GEMINI_API_KEY = islamic_config.gemini_api_key

                self.QURAN_QDRANT_URL = islamic_config.quran_qdrant_url
                self.HADITH_QDRANT_URL = islamic_config.hadith_qdrant_url
                self.TAFSEER_QDRANT_URL = islamic_config.tafseer_qdrant_url
                self.GENERAL_ISLAMIC_INFO_URL = islamic_config.general_islamic_info_url

                self.QURAN_QDRANT_API_KEY = islamic_config.quran_qdrant_api_key
                self.HADITH_QDRANT_API_KEY = islamic_config.hadith_qdrant_api_key
                self.TAFSEER_QDRANT_API_KEY = islamic_config.tafseer_qdrant_api_key
                self.GENERAL_ISLAMIC_INFO_KEY = islamic_config.general_islamic_info_key

        # Replace the settings in the ai_backend config module
        ai_backend_config.settings = MockSettings(self.islamic_config)

    def _create_qdrant_configs(self) -> dict:
        """
        Create Qdrant configuration dictionary for LanggraphService

        Returns:
            Dictionary with Qdrant configurations
        """
        return {
            "quran": {
                "url": self.islamic_config.quran_qdrant_url,
                "api_key": self.islamic_config.quran_qdrant_api_key,
                "collection": self.islamic_config.quran_collection_name,
            },
            "hadith": {
                "url": self.islamic_config.hadith_qdrant_url,
                "api_key": self.islamic_config.hadith_qdrant_api_key,
                "collection": self.islamic_config.hadith_collection_name,
            },
            "tafseer": {
                "url": self.islamic_config.tafseer_qdrant_url,
                "api_key": self.islamic_config.tafseer_qdrant_api_key,
                "collection": self.islamic_config.tafseer_collection_name,
            },
            "general_islamic_info": {
                "url": self.islamic_config.general_islamic_info_url,
                "api_key": self.islamic_config.general_islamic_info_key,
                "collection": self.islamic_config.islamic_info_collection_name,
            },
        }

    def process_query(self, query: str) -> str:
        """
        Process a user query using the Islamic AI backend

        This method adapts the ai_backend process_text_query workflow
        to work with the AIProcessor interface.

        Args:
            query: The user's input query

        Returns:
            AI-generated response string
        """
        if not query or not query.strip():
            return "I didn't hear anything. Could you please repeat that?"

        # Clean up the query
        user_input = query.strip()

        try:
            # Initialize services if not already done
            self._initialize_services()

            if not self._services_initialized:
                return "Sorry, the Islamic AI services are not available. Please check your configuration."

            # Step 1: Language detection and translation
            self.logger.info(f"Processing Islamic query: '{user_input}'")
            translation_result = self.deepl_service.detect_and_translate_query(user_input)

            if translation_result.get("status") != "success":
                self.logger.error(f"Translation failed: {translation_result.get('message', 'Unknown error')}")
                return "Sorry, I had trouble understanding your language. Please try again."

            processed_query = translation_result["processed_query"]
            detected_lang = translation_result["detected_language"]

            self.logger.info(f"Translated query: '{processed_query}', detected language: {detected_lang}")

            # Step 2: Query processing through LangGraph
            llm_response = self.langgraph_service.query(processed_query)

            if not llm_response:
                self.logger.error("Failed to generate LLM response")
                return "Sorry, I couldn't generate a response to your question. Please try again."

            # Step 3: Translate response back to original language
            final_response = self.deepl_service.translate_response(llm_response, detected_lang)

            self.logger.info("Islamic AI query processed successfully")
            return final_response

        except Exception as e:
            self.logger.error(f"Error processing Islamic AI query: {e}")
            return f"Sorry, I encountered an error while processing your request: {str(e)}"
