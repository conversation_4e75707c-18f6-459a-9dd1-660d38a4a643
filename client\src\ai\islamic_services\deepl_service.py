"""
DeepL Translation Service for Islamic AI

Handles language detection and translation using DeepL API.
Adapted from the original ai_backend implementation.
"""

import deepl
import logging
from typing import Dict, Any
from src.config.settings import IslamicAIConfig


class DeepLService:
    """Service for handling language detection and translation using DeepL API"""
    
    def __init__(self, config: IslamicAIConfig, openai_service=None):
        """
        Initialize DeepL service
        
        Args:
            config: Islamic AI configuration containing DeepL API key
            openai_service: OpenAI service for English detection (optional)
        """
        self.config = config
        self.openai_service = openai_service
        self.logger = logging.getLogger(__name__)
        
        if not config.deepl_api_key:
            self.logger.warning("DeepL API key not provided. Translation services will be disabled.")
            self.translator = None
        else:
            try:
                self.translator = deepl.Translator(auth_key=config.deepl_api_key)
                self.logger.info("DeepL service initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize DeepL translator: {e}")
                self.translator = None
    
    def detect_and_translate_query(self, query: str) -> Dict[str, Any]:
        """
        Detect language and translate query to English if needed
        
        Args:
            query: User input query
            
        Returns:
            Dictionary containing status, processed_query, detected_language, and translation_needed
        """
        if not self.translator:
            return {
                "status": "error",
                "message": "DeepL translator not available. Please check API key configuration."
            }
        
        try:
            # First check if already in English using OpenAI if available
            if self.openai_service:
                is_english = self.openai_service.is_english_with_llm(query)
                self.logger.info(f"English detection result: {is_english}")
                
                if is_english:
                    self.logger.info("English query detected successfully")
                    return {
                        "status": "success",
                        "processed_query": query,
                        "detected_language": "EN",
                        "translation_needed": False
                    }
            
            # Non-English query detected, translate
            self.logger.info("Non-English query detected, translating...")
            translated_query = self.translator.translate_text(query, target_lang="EN-US")
            detected_lang = translated_query.detected_source_lang
            
            self.logger.info(f"Detected language: {detected_lang}")
            
            # Handle specific language cases
            if detected_lang.upper() in ["RU", "UK"]:
                return {
                    "status": "success",
                    "processed_query": translated_query.text,
                    "detected_language": detected_lang.upper()
                }
            
            return {
                "status": "success",
                "processed_query": translated_query.text,
                "detected_language": detected_lang
            }
            
        except deepl.exceptions.AuthorizationException:
            return {
                "status": "error",
                "message": "DeepL API authentication failed. Please check your API key."
            }
        except deepl.exceptions.QuotaExceededException:
            return {
                "status": "error",
                "message": "DeepL API quota exceeded. Please try again later."
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to detect and translate input query: {str(e)}"
            }
    
    def translate_response(self, response: str, detected_lang: str) -> str:
        """
        Translate response back to the detected language
        
        Args:
            response: AI response in English
            detected_lang: Originally detected language code
            
        Returns:
            Translated response or original if translation not needed
        """
        if not self.translator:
            self.logger.warning("DeepL translator not available, returning original response")
            return response
            
        self.logger.info("Translating response back to original language")
        
        try:
            if detected_lang.upper() != "EN":
                # For now, defaulting to Russian for non-English languages
                # This can be enhanced to support more target languages
                translated = self.translator.translate_text(response, target_lang="RU")
                return translated.text
            
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to translate response: {e}")
            return response  # Return original response if translation fails
