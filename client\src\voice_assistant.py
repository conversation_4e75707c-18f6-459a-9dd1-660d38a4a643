"""
Main Voice Assistant Controller for the Hifzy Voice Assistant
Orchestrates all components and manages the application flow
"""

import logging
import time
# from typing import Optional
from src.config.settings import Config
from src.wake_word.detector import WakeWordDetector
from src.speech.recognizer import SpeechRecognizer
from src.ai.processor import <PERSON>P<PERSON>cessor<PERSON>actory, ConversationManager
from src.tts.synthesizer import TTSFactory


class VoiceAssistant:
    """
    Main controller that orchestrates all voice assistant components
    """

    def __init__(self, config: Config):
        """
        Initialize the voice assistant with configuration

        Args:
            config: Configuration object containing all settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.wake_word_detector = None
        self.speech_recognizer = None
        self.ai_processor = None
        self.tts_synthesizer = None
        self.conversation_manager = ConversationManager()

        # Control variables
        self.is_running = False
        self.is_processing = False
        self.shutdown_requested = False

        # Initialize all components
        self._initialize_components()

    def _initialize_components(self):
        """Initialize all voice assistant components"""
        try:
            self.logger.info("Initializing voice assistant components...")

            # Initialize wake word detector
            self.wake_word_detector = WakeWordDetector(self.config.wake_word, self.config.audio)
            self.wake_word_detector.set_wake_word_callback(self._on_wake_word_detected)

            # Initialize speech recognizer
            self.speech_recognizer = SpeechRecognizer(self.config.speech, self.config.audio)

            # Initialize AI processor
            self.ai_processor = AIProcessorFactory.create_processor(
                self.config.ai,
                processor_type="placeholder",  # Start with placeholder
                config=self.config,
            )

            # Initialize TTS synthesizer
            self.tts_synthesizer = TTSFactory.create_synthesizer(
                self.config.tts,
                synthesizer_type="gtts",  # Default to Windows TTS
            )

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise

    def start(self):
        """Start the voice assistant"""
        if self.is_running:
            self.logger.warning("Voice assistant is already running")
            return

        try:
            self.logger.info("Starting Hifzy Voice Assistant...")
            self.is_running = True

            # Test components before starting
            # if not self._test_components():
            #     self.logger.error("Component tests failed. Please check your audio setup.")
            #     return

            # Start wake word detection
            self.wake_word_detector.start_listening()

            # Welcome message
            welcome_msg = (
                f"Hello! I'm Ali, your voice assistant. Say '{self.config.wake_word.wake_word}' to activate me."
            )
            self.logger.info(welcome_msg)
            self.tts_synthesizer.speak(welcome_msg)

            # Main synchronous loop
            self._main_loop()

        except Exception as e:
            self.logger.error(f"Error starting voice assistant: {e}")
        finally:
            self.stop()

    def stop(self):
        """Stop the voice assistant"""
        if not self.is_running:
            return

        self.logger.info("Stopping voice assistant...")
        self.is_running = False
        self.shutdown_requested = True

        # Stop components
        if self.wake_word_detector:
            self.wake_word_detector.stop_listening()

        if self.tts_synthesizer:
            self.tts_synthesizer.stop_speaking()

        self.logger.info("Voice assistant stopped")

    def _main_loop(self):
        """Main synchronous application loop"""
        try:
            while self.is_running and not self.shutdown_requested:
                # Actively listen for wake word (synchronous)
                if not self.is_processing:
                    wake_word_detected = self.wake_word_detector.listen_for_wake_word()
                    if wake_word_detected:
                        # Wake word callback will be called automatically
                        pass
                else:
                    # If processing, sleep briefly to prevent high CPU usage
                    time.sleep(0.1)

        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Error in main loop: {e}")

    def _on_wake_word_detected(self):
        """Callback function called when wake word is detected"""
        if self.is_processing:
            self.logger.info("Already processing a command, ignoring wake word")
            return

        self.is_processing = True

        try:
            self.logger.info("Wake word detected! Processing command...")

            # Give audio feedback
            self.tts_synthesizer.speak("Yes?")

            # Wait a moment for TTS to finish
            time.sleep(2)

            # Listen for the user's command
            success, command = self.speech_recognizer.listen_for_command_with_feedback()

            if success and command:
                # Process the command with AI
                response = self.ai_processor.process_query(command)

                # Add to conversation history
                self.conversation_manager.add_interaction(command, response)

                # Speak the response
                self.tts_synthesizer.speak(response)

                self.logger.info(f"Command processed successfully: '{command}' -> '{response}'")

            elif command:  # Error message from speech recognizer
                self.tts_synthesizer.speak(command)
            else:
                # No speech detected
                self.tts_synthesizer.speak("I didn't hear anything. Try saying my wake word again.")

        except Exception as e:
            self.logger.error(f"Error processing wake word: {e}")
            self.tts_synthesizer.speak("Sorry, I encountered an error. Please try again.")
        finally:
            self.is_processing = False

    def _test_components(self) -> bool:
        """
        Test all components to ensure they're working

        Returns:
            True if all tests pass, False otherwise
        """
        self.logger.info("Testing voice assistant components...")

        try:
            # Test microphone
            if not self.wake_word_detector.test_microphone():
                self.logger.error("Microphone test failed")
                return False

            # Test TTS
            self.tts_synthesizer.speak("Testing text to speech")
            time.sleep(2)  # Give time for TTS to complete

            self.logger.info("All component tests passed")
            return True

        except Exception as e:
            self.logger.error(f"Component testing failed: {e}")
            return False

    def process_text_command(self, command: str) -> str:
        """
        Process a text command directly (useful for testing)

        Args:
            command: Text command to process

        Returns:
            AI response string
        """
        try:
            response = self.ai_processor.process_query(command)
            self.conversation_manager.add_interaction(command, response)
            return response
        except Exception as e:
            self.logger.error(f"Error processing text command: {e}")
            return "Sorry, I encountered an error processing your command."

    def get_conversation_history(self) -> list:
        """Get the conversation history"""
        return self.conversation_manager.conversation_history

    def clear_conversation_history(self):
        """Clear the conversation history"""
        self.conversation_manager.clear_history()
        self.logger.info("Conversation history cleared")

    def set_ai_processor_type(self, processor_type: str):
        """
        Change the AI processor type

        Args:
            processor_type: Type of processor ("placeholder", "openai", or "islamic")
        """
        try:
            self.ai_processor = AIProcessorFactory.create_processor(
                self.config.ai, processor_type=processor_type, config=self.config
            )
            self.logger.info(f"AI processor changed to: {processor_type}")
        except Exception as e:
            self.logger.error(f"Failed to change AI processor: {e}")

    def set_tts_type(self, tts_type: str):
        """
        Change the TTS synthesizer type

        Args:
            tts_type: Type of TTS ("windows" or "gtts")
        """
        try:
            # Stop current TTS
            if self.tts_synthesizer:
                self.tts_synthesizer.stop_speaking()

            # Create new TTS
            self.tts_synthesizer = TTSFactory.create_synthesizer(self.config.tts, synthesizer_type=tts_type)
            self.logger.info(f"TTS synthesizer changed to: {tts_type}")
        except Exception as e:
            self.logger.error(f"Failed to change TTS synthesizer: {e}")
